#!/usr/bin/env python3
"""
模糊时空数据查询示例
展示如何通过确定性索引查询模糊时空实体
"""

import psycopg2
import json
from tabulate import tabulate
from datetime import datetime, date

class FuzzySpatiotemporalQueries:
    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.conn = None
        self.cursor = None
    
    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.cursor = self.conn.cursor()
            print(f"✅ 连接成功: {self.db_config['database']}")
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            raise
    
    def disconnect(self):
        """断开连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
    
    def query_temporal_range(self, start_year: int, end_year: int, min_confidence: float = 0.5):
        """查询特定时间范围内的所有实体（包括模糊时间）"""
        print(f"\n🕐 查询 {start_year}-{end_year} 年间的实体 (置信度 >= {min_confidence})")
        
        sql = """
        SELECT DISTINCT
            e.title as 实体名称,
            e.type as 实体类型,
            e.fuzziness_type as 模糊类型,
            tm.interpretation as 时间解释,
            tm.start_date as 开始日期,
            tm.end_date as 结束日期,
            tm.confidence as 置信度
        FROM fuzzy_spatiotemporal_entities e
        JOIN fuzzy_temporal_mappings tm ON e.id = tm.entity_id
        WHERE tm.time_range && daterange(%s, %s)
          AND tm.confidence >= %s
        ORDER BY tm.confidence DESC, e.title;
        """
        
        self.cursor.execute(sql, (f"{start_year}-01-01", f"{end_year}-12-31", min_confidence))
        results = self.cursor.fetchall()
        
        if results:
            headers = ["实体名称", "类型", "模糊类型", "时间解释", "开始日期", "结束日期", "置信度"]
            print(tabulate(results, headers=headers, tablefmt="grid"))
        else:
            print("   没有找到匹配的实体")
        
        return results
    
    def query_spatial_region(self, country: str = None, lat_range: tuple = None, lon_range: tuple = None, min_confidence: float = 0.5):
        """查询特定空间区域内的实体"""
        conditions = ["sm.confidence >= %s"]
        params = [min_confidence]
        
        if country:
            conditions.append("sm.country ILIKE %s")
            params.append(f"%{country}%")
            print(f"\n🗺️  查询 {country} 的实体 (置信度 >= {min_confidence})")
        
        if lat_range and lon_range:
            conditions.extend([
                "sm.latitude BETWEEN %s AND %s",
                "sm.longitude BETWEEN %s AND %s"
            ])
            params.extend([lat_range[0], lat_range[1], lon_range[0], lon_range[1]])
            print(f"\n🗺️  查询坐标范围内的实体: 纬度{lat_range}, 经度{lon_range}")
        
        sql = f"""
        SELECT DISTINCT
            e.title as 实体名称,
            e.type as 实体类型,
            e.fuzziness_type as 模糊类型,
            sm.interpretation as 位置解释,
            sm.latitude as 纬度,
            sm.longitude as 经度,
            sm.country as 国家,
            sm.confidence as 置信度
        FROM fuzzy_spatiotemporal_entities e
        JOIN fuzzy_spatial_mappings sm ON e.id = sm.entity_id
        WHERE {' AND '.join(conditions)}
        ORDER BY sm.confidence DESC, e.title;
        """
        
        self.cursor.execute(sql, params)
        results = self.cursor.fetchall()
        
        if results:
            headers = ["实体名称", "类型", "模糊类型", "位置解释", "纬度", "经度", "国家", "置信度"]
            print(tabulate(results, headers=headers, tablefmt="grid"))
        else:
            print("   没有找到匹配的实体")
        
        return results
    
    def query_fuzzy_entities_by_type(self, fuzziness_type: str):
        """查询特定模糊类型的实体"""
        print(f"\n🔍 查询模糊类型为 '{fuzziness_type}' 的实体")
        
        sql = """
        SELECT 
            title as 实体名称,
            type as 实体类型,
            fuzziness_type as 模糊类型,
            candidate_mappings as 候选映射
        FROM fuzzy_spatiotemporal_entities
        WHERE fuzziness_type = %s
        ORDER BY title;
        """
        
        self.cursor.execute(sql, (fuzziness_type,))
        results = self.cursor.fetchall()
        
        if results:
            # 处理候选映射的显示
            processed_results = []
            for row in results:
                title, entity_type, fuzz_type, mappings_json = row
                try:
                    mappings = json.loads(mappings_json) if mappings_json else []
                    mappings_summary = "; ".join([m.get('interpretation', '') for m in mappings[:3]])
                    if len(mappings) > 3:
                        mappings_summary += f" (共{len(mappings)}个候选)"
                except:
                    mappings_summary = "解析失败"
                
                processed_results.append([title, entity_type, fuzz_type, mappings_summary])
            
            headers = ["实体名称", "类型", "模糊类型", "候选映射摘要"]
            print(tabulate(processed_results, headers=headers, tablefmt="grid"))
        else:
            print("   没有找到匹配的实体")
        
        return results
    
    def query_spatiotemporal_intersection(self, time_range: tuple, spatial_filter: dict, min_confidence: float = 0.6):
        """查询时空交集：特定时间和地点的实体"""
        start_year, end_year = time_range
        print(f"\n🌍⏰ 查询 {start_year}-{end_year} 年间在特定地点的实体")
        
        # 构建空间过滤条件
        spatial_conditions = []
        spatial_params = []
        
        if spatial_filter.get('country'):
            spatial_conditions.append("sm.country ILIKE %s")
            spatial_params.append(f"%{spatial_filter['country']}%")
        
        if spatial_filter.get('region'):
            spatial_conditions.append("sm.region ILIKE %s")
            spatial_params.append(f"%{spatial_filter['region']}%")
        
        spatial_where = " AND ".join(spatial_conditions) if spatial_conditions else "TRUE"
        
        sql = f"""
        SELECT DISTINCT
            e.title as 实体名称,
            e.type as 实体类型,
            tm.interpretation as 时间解释,
            tm.start_date as 时间,
            sm.interpretation as 位置解释,
            sm.country as 国家,
            (tm.confidence + sm.confidence) / 2 as 综合置信度
        FROM fuzzy_spatiotemporal_entities e
        JOIN fuzzy_temporal_mappings tm ON e.id = tm.entity_id
        JOIN fuzzy_spatial_mappings sm ON e.id = sm.entity_id
        WHERE tm.time_range && daterange(%s, %s)
          AND tm.confidence >= %s
          AND sm.confidence >= %s
          AND {spatial_where}
        ORDER BY (tm.confidence + sm.confidence) / 2 DESC;
        """
        
        params = [f"{start_year}-01-01", f"{end_year}-12-31", min_confidence, min_confidence] + spatial_params
        
        self.cursor.execute(sql, params)
        results = self.cursor.fetchall()
        
        if results:
            headers = ["实体名称", "类型", "时间解释", "时间", "位置解释", "国家", "综合置信度"]
            print(tabulate(results, headers=headers, tablefmt="grid"))
        else:
            print("   没有找到匹配的时空交集实体")
        
        return results
    
    def analyze_fuzzy_coverage(self):
        """分析模糊性覆盖情况"""
        print("\n📊 模糊时空数据覆盖分析")
        
        # 总体统计
        self.cursor.execute("""
            SELECT 
                COUNT(*) as 总实体数,
                COUNT(CASE WHEN type = 'TIME' THEN 1 END) as 时间实体数,
                COUNT(CASE WHEN type = 'LOCATION' THEN 1 END) as 位置实体数,
                COUNT(CASE WHEN fuzziness_type IS NOT NULL THEN 1 END) as 有模糊信息的实体数
            FROM fuzzy_spatiotemporal_entities;
        """)
        
        stats = self.cursor.fetchone()
        print(f"总实体数: {stats[0]}")
        print(f"时间实体数: {stats[1]}")
        print(f"位置实体数: {stats[2]}")
        print(f"有模糊信息的实体数: {stats[3]}")
        
        # 模糊类型分布
        print("\n模糊类型分布:")
        self.cursor.execute("""
            SELECT fuzziness_type, COUNT(*) as 数量
            FROM fuzzy_spatiotemporal_entities
            WHERE fuzziness_type IS NOT NULL
            GROUP BY fuzziness_type
            ORDER BY COUNT(*) DESC;
        """)
        
        fuzz_types = self.cursor.fetchall()
        for fuzz_type, count in fuzz_types:
            print(f"  {fuzz_type}: {count}")
        
        # 候选映射统计
        print("\n候选映射统计:")
        self.cursor.execute("""
            SELECT 
                COUNT(*) as 时间映射总数,
                AVG(confidence) as 平均置信度,
                MIN(confidence) as 最低置信度,
                MAX(confidence) as 最高置信度
            FROM fuzzy_temporal_mappings;
        """)
        
        temporal_stats = self.cursor.fetchone()
        if temporal_stats[0]:
            print(f"  时间映射: {temporal_stats[0]}个, 平均置信度: {temporal_stats[1]:.2f}")
        
        self.cursor.execute("""
            SELECT 
                COUNT(*) as 空间映射总数,
                AVG(confidence) as 平均置信度,
                MIN(confidence) as 最低置信度,
                MAX(confidence) as 最高置信度
            FROM fuzzy_spatial_mappings;
        """)
        
        spatial_stats = self.cursor.fetchone()
        if spatial_stats[0]:
            print(f"  空间映射: {spatial_stats[0]}个, 平均置信度: {spatial_stats[1]:.2f}")
    
    def demo_queries(self):
        """演示查询功能"""
        print("🚀 模糊时空查询系统演示")
        print("=" * 50)
        
        # 1. 分析覆盖情况
        self.analyze_fuzzy_coverage()
        
        # 2. 查询20世纪初的实体
        self.query_temporal_range(1900, 1920, min_confidence=0.5)
        
        # 3. 查询德国的实体
        self.query_spatial_region(country="Germany", min_confidence=0.5)
        
        # 4. 查询相对模糊的时间实体
        self.query_fuzzy_entities_by_type("relative_contextual")
        
        # 5. 查询时空交集：20世纪初的德国实体
        self.query_spatiotemporal_intersection(
            time_range=(1900, 1920),
            spatial_filter={"country": "Germany"},
            min_confidence=0.5
        )

def main():
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'graphrag',
        'user': 'postgres',
        'password': ''
    }
    
    queries = FuzzySpatiotemporalQueries(db_config)
    
    try:
        queries.connect()
        queries.demo_queries()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    finally:
        queries.disconnect()

if __name__ == "__main__":
    main()
