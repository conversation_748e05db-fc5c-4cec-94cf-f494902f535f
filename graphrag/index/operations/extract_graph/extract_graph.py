# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""A module containing entity_extract methods."""

import logging
import re
import json
from typing import Any
from datetime import datetime

import pandas as pd

from graphrag.cache.pipeline_cache import PipelineCache
from graphrag.callbacks.workflow_callbacks import WorkflowCallbacks
from graphrag.config.enums import AsyncType
from graphrag.index.operations.extract_graph.typing import (
    Document,
    EntityExtractStrategy,
    ExtractEntityStrategyType,
)
from graphrag.index.utils.derive_from_rows import derive_from_rows

log = logging.getLogger(__name__)


DEFAULT_ENTITY_TYPES = ["organization", "person", "geo", "event", "time", "location"]


async def extract_graph(
    text_units: pd.DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    text_column: str,
    id_column: str,
    strategy: dict[str, Any] | None,
    async_mode: AsyncType = AsyncType.AsyncIO,
    entity_types=DEFAULT_ENTITY_TYPES,
    num_threads: int = 4,
) -> tuple[pd.<PERSON>Frame, pd.DataFrame]:
    """Extract a graph from a piece of text using a language model."""
    log.debug("entity_extract strategy=%s", strategy)
    if entity_types is None:
        entity_types = DEFAULT_ENTITY_TYPES
    strategy = strategy or {}
    strategy_exec = _load_strategy(
        strategy.get("type", ExtractEntityStrategyType.graph_intelligence)
    )
    strategy_config = {**strategy}

    num_started = 0

    async def run_strategy(row):
        nonlocal num_started
        text = row[text_column]
        id = row[id_column]
        result = await strategy_exec(
            [Document(text=text, id=id)],
            entity_types,
            callbacks,
            cache,
            strategy_config,
        )
        num_started += 1
        return [result.entities, result.relationships, result.graph]

    results = await derive_from_rows(
        text_units,
        run_strategy,
        callbacks,
        async_type=async_mode,
        num_threads=num_threads,
    )

    entity_dfs = []
    relationship_dfs = []
    for result in results:
        if result:
            entity_dfs.append(pd.DataFrame(result[0]))
            relationship_dfs.append(pd.DataFrame(result[1]))

    entities = _merge_entities(entity_dfs)
    relationships = _merge_relationships(relationship_dfs)

    return (entities, relationships)


def _load_strategy(strategy_type: ExtractEntityStrategyType) -> EntityExtractStrategy:
    """Load strategy method definition."""
    match strategy_type:
        case ExtractEntityStrategyType.graph_intelligence:
            from graphrag.index.operations.extract_graph.graph_intelligence_strategy import (
                run_graph_intelligence,
            )

            return run_graph_intelligence

        case _:
            msg = f"Unknown strategy: {strategy_type}"
            raise ValueError(msg)


def _merge_entities(entity_dfs) -> pd.DataFrame:
    all_entities = pd.concat(entity_dfs, ignore_index=True)

    # Group by title and type
    grouped = all_entities.groupby(["title", "type"], sort=False)

    # Aggregate descriptions and source_ids
    result = grouped.agg({
        "description": list,
        "source_id": [list, "count"] if "source_id" in all_entities.columns else list,
    }).reset_index()

    # Flatten column names
    result.columns = ["title", "type", "description", "text_unit_ids", "frequency"]

    # Process descriptions - join unique descriptions
    result["description"] = result["description"].apply(
        lambda x: "\n".join(list(set(x))) if isinstance(x, list) else x
    )

    # Add fuzzy spatiotemporal resolution for TIME and LOCATION entities
    result = _resolve_fuzzy_spatiotemporal(result)

    return result


def _resolve_fuzzy_spatiotemporal(entities_df: pd.DataFrame) -> pd.DataFrame:
    """Resolve fuzzy spatiotemporal entities to multiple candidate mappings."""

    # Add columns for fuzzy resolution
    entities_df["fuzziness_type"] = None
    entities_df["candidate_mappings"] = None
    entities_df["confidence_scores"] = None

    for idx, row in entities_df.iterrows():
        if row["type"] == "TIME":
            fuzziness, candidates = _resolve_temporal_fuzziness(row["title"], row["description"])
            entities_df.at[idx, "fuzziness_type"] = fuzziness
            entities_df.at[idx, "candidate_mappings"] = json.dumps(candidates)
            entities_df.at[idx, "confidence_scores"] = json.dumps([c.get("confidence", 0.5) for c in candidates])

        elif row["type"] == "LOCATION":
            fuzziness, candidates = _resolve_spatial_fuzziness(row["title"], row["description"])
            entities_df.at[idx, "fuzziness_type"] = fuzziness
            entities_df.at[idx, "candidate_mappings"] = json.dumps(candidates)
            entities_df.at[idx, "confidence_scores"] = json.dumps([c.get("confidence", 0.5) for c in candidates])

    return entities_df


def _resolve_temporal_fuzziness(title: str, description: str):
    """Resolve temporal fuzziness to candidate time periods."""

    # Patterns for different types of temporal fuzziness
    patterns = {
        "absolute_precise": r"^\d{4}年?$",  # 1905年
        "absolute_fuzzy": r"^\d{4}年代$",   # 1900年代
        "relative_contextual": r"(那个?|这个?)(时候|年代|时期)",  # 那个年代
        "cultural_period": r"(春秋|战国|民国|文艺复兴|工业革命)",  # 春秋时期
        "subjective_time": r"(童年|青年|晚年|老年)",  # 童年时代
        "relative_age": r"\d+岁",  # 76岁
    }

    for fuzz_type, pattern in patterns.items():
        if re.search(pattern, title):
            return fuzz_type, _get_temporal_candidates(title, fuzz_type, description)

    return "unknown", [{"interpretation": title, "confidence": 0.5}]


def _get_temporal_candidates(title: str, fuzz_type: str, description: str):
    """Get candidate temporal mappings based on fuzziness type."""

    candidates = []

    if fuzz_type == "absolute_precise":
        year = re.findall(r"\d{4}", title)[0]
        candidates.append({
            "interpretation": f"{year}年",
            "start_date": f"{year}-01-01",
            "end_date": f"{year}-12-31",
            "granularity": "YEAR",
            "confidence": 0.95
        })

    elif fuzz_type == "relative_contextual":
        # 基于描述中的线索推断可能的时期
        if "科学" in description or "相对论" in description:
            candidates.extend([
                {
                    "interpretation": "科学革命时期",
                    "start_date": "1900-01-01",
                    "end_date": "1920-12-31",
                    "granularity": "PERIOD",
                    "confidence": 0.7
                },
                {
                    "interpretation": "现代物理学诞生期",
                    "start_date": "1905-01-01",
                    "end_date": "1915-12-31",
                    "granularity": "DECADE",
                    "confidence": 0.6
                }
            ])
        else:
            # 通用的"动荡年代"候选
            candidates.extend([
                {
                    "interpretation": "第一次世界大战",
                    "start_date": "1914-01-01",
                    "end_date": "1918-12-31",
                    "granularity": "PERIOD",
                    "confidence": 0.4
                },
                {
                    "interpretation": "第二次世界大战",
                    "start_date": "1939-01-01",
                    "end_date": "1945-12-31",
                    "granularity": "PERIOD",
                    "confidence": 0.3
                }
            ])

    elif fuzz_type == "cultural_period":
        period_mappings = {
            "春秋": {"start": "0770-01-01", "end": "0476-12-31", "confidence": 0.8},
            "战国": {"start": "0475-01-01", "end": "0221-12-31", "confidence": 0.8},
            "民国": {"start": "1912-01-01", "end": "1949-12-31", "confidence": 0.9},
            "文艺复兴": {"start": "1400-01-01", "end": "1600-12-31", "confidence": 0.7},
            "工业革命": {"start": "1760-01-01", "end": "1840-12-31", "confidence": 0.7}
        }

        for period, mapping in period_mappings.items():
            if period in title:
                candidates.append({
                    "interpretation": f"{period}时期",
                    "start_date": mapping["start"],
                    "end_date": mapping["end"],
                    "granularity": "HISTORICAL_PERIOD",
                    "confidence": mapping["confidence"]
                })

    elif fuzz_type == "relative_age":
        age = re.findall(r"\d+", title)[0]
        candidates.append({
            "interpretation": f"{age}岁",
            "relative_type": "AGE",
            "duration_years": int(age),
            "granularity": "RELATIVE_AGE",
            "confidence": 0.9,
            "note": "需要主体信息才能确定绝对时间"
        })

    # 如果没有找到候选，返回默认
    if not candidates:
        candidates.append({
            "interpretation": title,
            "confidence": 0.3,
            "note": "未识别的时间表达"
        })

    return candidates


def _resolve_spatial_fuzziness(title: str, description: str):
    """Resolve spatial fuzziness to candidate locations."""

    patterns = {
        "country": r"^(中国|美国|德国|英国|法国|日本|CHINA|USA|GERMANY|BRITAIN|FRANCE|JAPAN)$",
        "city": r"(北京|上海|纽约|伦敦|巴黎|东京|柏林|慕尼黑|苏黎世|普林斯顿)",
        "cultural_region": r"(江南|中原|西域|东北|华南|华北)",
        "relative_location": r"(附近|远方|天涯海角|故乡|他乡)",
        "functional_space": r"(家|学校|办公室|实验室|图书馆)"
    }

    for fuzz_type, pattern in patterns.items():
        if re.search(pattern, title):
            return fuzz_type, _get_spatial_candidates(title, fuzz_type, description)

    return "unknown", [{"interpretation": title, "confidence": 0.5}]


def _get_spatial_candidates(title: str, fuzz_type: str, description: str):
    """Get candidate spatial mappings based on fuzziness type."""

    candidates = []

    if fuzz_type == "country":
        country_mappings = {
            "德国": {"standard": "Germany", "coords": [51.1657, 10.4515], "confidence": 0.9},
            "美国": {"standard": "United States", "coords": [39.8283, -98.5795], "confidence": 0.9},
            "中国": {"standard": "China", "coords": [35.8617, 104.1954], "confidence": 0.9},
            "GERMANY": {"standard": "Germany", "coords": [51.1657, 10.4515], "confidence": 0.95}
        }

        for country, mapping in country_mappings.items():
            if country in title:
                candidates.append({
                    "interpretation": mapping["standard"],
                    "latitude": mapping["coords"][0],
                    "longitude": mapping["coords"][1],
                    "location_type": "COUNTRY",
                    "confidence": mapping["confidence"]
                })

    elif fuzz_type == "city":
        city_mappings = {
            "乌尔姆": {"standard": "Ulm", "coords": [48.4011, 9.9876], "country": "Germany"},
            "苏黎世": {"standard": "Zurich", "coords": [47.3769, 8.5417], "country": "Switzerland"},
            "普林斯顿": {"standard": "Princeton", "coords": [40.3573, -74.6672], "country": "USA"},
            "柏林": {"standard": "Berlin", "coords": [52.5200, 13.4050], "country": "Germany"}
        }

        for city, mapping in city_mappings.items():
            if city in title:
                candidates.append({
                    "interpretation": mapping["standard"],
                    "latitude": mapping["coords"][0],
                    "longitude": mapping["coords"][1],
                    "location_type": "CITY",
                    "country": mapping["country"],
                    "confidence": 0.85
                })

    elif fuzz_type == "cultural_region":
        region_mappings = {
            "江南": {
                "interpretation": "江南地区",
                "bbox": [[30.0, 118.0], [32.0, 122.0]],  # 大致范围
                "confidence": 0.6,
                "note": "文化地理概念，边界模糊"
            },
            "中原": {
                "interpretation": "中原地区",
                "bbox": [[33.0, 112.0], [36.0, 116.0]],
                "confidence": 0.6,
                "note": "历史文化概念"
            }
        }

        for region, mapping in region_mappings.items():
            if region in title:
                candidates.append(mapping)

    elif fuzz_type == "relative_location":
        candidates.append({
            "interpretation": title,
            "location_type": "RELATIVE",
            "confidence": 0.4,
            "note": "需要参考点才能确定具体位置"
        })

    # 默认候选
    if not candidates:
        candidates.append({
            "interpretation": title,
            "confidence": 0.3,
            "note": "未识别的空间表达"
        })

    return candidates


def _merge_relationships(relationship_dfs) -> pd.DataFrame:
    all_relationships = pd.concat(relationship_dfs, ignore_index=False)
    return (
        all_relationships.groupby(["source", "target"], sort=False)
        .agg(
            description=("description", list),
            text_unit_ids=("source_id", list),
            weight=("weight", "sum"),
        )
        .reset_index()
    )
