#!/usr/bin/env python3
"""
模糊时空数据的PostgreSQL存储和索引系统
"""

import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import json
import os
from typing import Optional, List, Dict, Any
import argparse
from datetime import datetime

class FuzzySpatiotemporalStorage:
    def __init__(self, db_config: dict):
        """初始化模糊时空存储系统"""
        self.db_config = db_config
        self.conn = None
        self.cursor = None
    
    def connect(self):
        """连接到PostgreSQL数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.cursor = self.conn.cursor()
            print(f"✅ 成功连接到PostgreSQL数据库: {self.db_config['database']}")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            raise
    
    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("✅ 数据库连接已关闭")
    
    def create_fuzzy_spatiotemporal_tables(self, drop_if_exists: bool = False):
        """创建模糊时空数据表"""
        try:
            if drop_if_exists:
                drop_sqls = [
                    "DROP TABLE IF EXISTS fuzzy_temporal_mappings CASCADE;",
                    "DROP TABLE IF EXISTS fuzzy_spatial_mappings CASCADE;", 
                    "DROP TABLE IF EXISTS fuzzy_spatiotemporal_entities CASCADE;"
                ]
                for sql in drop_sqls:
                    self.cursor.execute(sql)
                print("🗑️  已删除现有的模糊时空表")
            
            # 主实体表（包含模糊信息）
            create_main_table = """
            CREATE TABLE IF NOT EXISTS fuzzy_spatiotemporal_entities (
                id VARCHAR(255) PRIMARY KEY,
                human_readable_id INTEGER,
                title VARCHAR(500) NOT NULL,
                type VARCHAR(100),
                description TEXT,
                text_unit_ids TEXT[],
                frequency INTEGER,
                degree INTEGER,
                x FLOAT,
                y FLOAT,
                
                -- 模糊性信息
                fuzziness_type VARCHAR(100),
                candidate_mappings TEXT,  -- JSON格式存储候选映射
                confidence_scores TEXT,   -- JSON格式存储置信度
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            # 时间候选映射表（用于索引确定性时间）
            create_temporal_mappings = """
            CREATE TABLE IF NOT EXISTS fuzzy_temporal_mappings (
                id SERIAL PRIMARY KEY,
                entity_id VARCHAR(255) REFERENCES fuzzy_spatiotemporal_entities(id),
                
                -- 候选时间信息
                interpretation TEXT,
                start_date DATE,
                end_date DATE,
                granularity VARCHAR(50),  -- YEAR, PERIOD, DECADE, etc.
                confidence FLOAT,
                
                -- 时间范围（用于高效查询）
                time_range DATERANGE,
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            # 空间候选映射表（用于索引确定性位置）
            create_spatial_mappings = """
            CREATE TABLE IF NOT EXISTS fuzzy_spatial_mappings (
                id SERIAL PRIMARY KEY,
                entity_id VARCHAR(255) REFERENCES fuzzy_spatiotemporal_entities(id),
                
                -- 候选空间信息
                interpretation TEXT,
                latitude FLOAT,
                longitude FLOAT,
                location_type VARCHAR(50),  -- COUNTRY, CITY, REGION, etc.
                confidence FLOAT,
                
                -- PostGIS几何字段（如果安装了PostGIS）
                -- geometry GEOMETRY(POINT, 4326),
                -- bbox GEOMETRY(POLYGON, 4326),
                
                -- 层级信息
                country VARCHAR(100),
                region VARCHAR(100),
                city VARCHAR(100),
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            tables = [create_main_table, create_temporal_mappings, create_spatial_mappings]
            for sql in tables:
                self.cursor.execute(sql)
            
            # 创建索引
            indexes = [
                # 主表索引
                "CREATE INDEX IF NOT EXISTS idx_fuzzy_entities_type ON fuzzy_spatiotemporal_entities(type);",
                "CREATE INDEX IF NOT EXISTS idx_fuzzy_entities_fuzziness ON fuzzy_spatiotemporal_entities(fuzziness_type);",
                
                # 时间映射索引
                "CREATE INDEX IF NOT EXISTS idx_temporal_mappings_entity ON fuzzy_temporal_mappings(entity_id);",
                "CREATE INDEX IF NOT EXISTS idx_temporal_mappings_range ON fuzzy_temporal_mappings USING GIST(time_range);",
                "CREATE INDEX IF NOT EXISTS idx_temporal_mappings_dates ON fuzzy_temporal_mappings(start_date, end_date);",
                "CREATE INDEX IF NOT EXISTS idx_temporal_mappings_confidence ON fuzzy_temporal_mappings(confidence);",
                
                # 空间映射索引
                "CREATE INDEX IF NOT EXISTS idx_spatial_mappings_entity ON fuzzy_spatial_mappings(entity_id);",
                "CREATE INDEX IF NOT EXISTS idx_spatial_mappings_coords ON fuzzy_spatial_mappings(latitude, longitude);",
                "CREATE INDEX IF NOT EXISTS idx_spatial_mappings_country ON fuzzy_spatial_mappings(country);",
                "CREATE INDEX IF NOT EXISTS idx_spatial_mappings_confidence ON fuzzy_spatial_mappings(confidence);",
            ]
            
            for index_sql in indexes:
                self.cursor.execute(index_sql)
            
            self.conn.commit()
            print("✅ 成功创建模糊时空数据表和索引")
            
        except Exception as e:
            self.conn.rollback()
            print(f"❌ 创建表失败: {e}")
            raise
    
    def load_and_process_entities(self, parquet_path: str):
        """加载并处理GraphRAG实体数据"""
        if not os.path.exists(parquet_path):
            raise FileNotFoundError(f"找不到文件: {parquet_path}")
        
        df = pd.read_parquet(parquet_path)
        print(f"📊 从 {parquet_path} 加载了 {len(df)} 个实体")
        
        # 处理主实体数据
        main_data = []
        temporal_mappings = []
        spatial_mappings = []
        
        for _, row in df.iterrows():
            # 处理主实体
            main_record = self._prepare_main_entity_record(row)
            main_data.append(main_record)
            
            # 处理时空映射
            if row.get('type') == 'TIME' and row.get('candidate_mappings'):
                temporal_mappings.extend(
                    self._prepare_temporal_mappings(row['id'], row.get('candidate_mappings'))
                )
            
            if row.get('type') == 'LOCATION' and row.get('candidate_mappings'):
                spatial_mappings.extend(
                    self._prepare_spatial_mappings(row['id'], row.get('candidate_mappings'))
                )
        
        return main_data, temporal_mappings, spatial_mappings
    
    def _prepare_main_entity_record(self, row):
        """准备主实体记录"""
        def safe_value(value):
            return None if pd.isna(value) else value
        
        # 处理text_unit_ids
        text_unit_ids = row.get('text_unit_ids', [])
        if not isinstance(text_unit_ids, list):
            text_unit_ids = [str(text_unit_ids)] if text_unit_ids else []
        
        return (
            str(row['id']),
            int(row['human_readable_id']) if pd.notna(row.get('human_readable_id')) else None,
            str(row['title']),
            safe_value(row.get('type')),
            safe_value(row.get('description')),
            text_unit_ids,
            int(row['frequency']) if pd.notna(row.get('frequency')) else None,
            int(row['degree']) if pd.notna(row.get('degree')) else None,
            float(row['x']) if pd.notna(row.get('x')) else None,
            float(row['y']) if pd.notna(row.get('y')) else None,
            safe_value(row.get('fuzziness_type')),
            safe_value(row.get('candidate_mappings')),
            safe_value(row.get('confidence_scores')),
            datetime.now()
        )
    
    def _prepare_temporal_mappings(self, entity_id: str, candidate_mappings_json: str) -> List[tuple]:
        """准备时间映射记录"""
        mappings = []
        
        try:
            candidates = json.loads(candidate_mappings_json) if candidate_mappings_json else []
            
            for candidate in candidates:
                start_date = candidate.get('start_date')
                end_date = candidate.get('end_date')
                
                # 创建时间范围
                time_range = None
                if start_date and end_date:
                    time_range = f"[{start_date},{end_date}]"
                
                mapping = (
                    entity_id,
                    candidate.get('interpretation', ''),
                    start_date,
                    end_date,
                    candidate.get('granularity', ''),
                    float(candidate.get('confidence', 0.5)),
                    time_range,
                    datetime.now()
                )
                mappings.append(mapping)
                
        except (json.JSONDecodeError, Exception) as e:
            print(f"⚠️  解析时间映射失败 {entity_id}: {e}")
        
        return mappings
    
    def _prepare_spatial_mappings(self, entity_id: str, candidate_mappings_json: str) -> List[tuple]:
        """准备空间映射记录"""
        mappings = []
        
        try:
            candidates = json.loads(candidate_mappings_json) if candidate_mappings_json else []
            
            for candidate in candidates:
                mapping = (
                    entity_id,
                    candidate.get('interpretation', ''),
                    float(candidate.get('latitude')) if candidate.get('latitude') else None,
                    float(candidate.get('longitude')) if candidate.get('longitude') else None,
                    candidate.get('location_type', ''),
                    float(candidate.get('confidence', 0.5)),
                    candidate.get('country', ''),
                    candidate.get('region', ''),
                    candidate.get('city', ''),
                    datetime.now()
                )
                mappings.append(mapping)
                
        except (json.JSONDecodeError, Exception) as e:
            print(f"⚠️  解析空间映射失败 {entity_id}: {e}")
        
        return mappings
    
    def insert_data(self, main_data: List[tuple], temporal_mappings: List[tuple], spatial_mappings: List[tuple]):
        """批量插入数据"""
        try:
            # 插入主实体数据
            if main_data:
                main_insert_sql = """
                INSERT INTO fuzzy_spatiotemporal_entities (
                    id, human_readable_id, title, type, description, text_unit_ids,
                    frequency, degree, x, y, fuzziness_type, candidate_mappings,
                    confidence_scores, created_at
                ) VALUES %s
                ON CONFLICT (id) DO UPDATE SET
                    title = EXCLUDED.title,
                    type = EXCLUDED.type,
                    description = EXCLUDED.description,
                    fuzziness_type = EXCLUDED.fuzziness_type,
                    candidate_mappings = EXCLUDED.candidate_mappings,
                    confidence_scores = EXCLUDED.confidence_scores;
                """
                
                execute_values(self.cursor, main_insert_sql, main_data, page_size=100)
                print(f"✅ 插入 {len(main_data)} 条主实体记录")
            
            # 插入时间映射
            if temporal_mappings:
                # 先删除现有映射
                self.cursor.execute("DELETE FROM fuzzy_temporal_mappings WHERE entity_id IN %s", 
                                  (tuple(set(m[0] for m in temporal_mappings)),))
                
                temporal_insert_sql = """
                INSERT INTO fuzzy_temporal_mappings (
                    entity_id, interpretation, start_date, end_date, granularity,
                    confidence, time_range, created_at
                ) VALUES %s;
                """
                
                execute_values(self.cursor, temporal_insert_sql, temporal_mappings, page_size=100)
                print(f"✅ 插入 {len(temporal_mappings)} 条时间映射记录")
            
            # 插入空间映射
            if spatial_mappings:
                # 先删除现有映射
                self.cursor.execute("DELETE FROM fuzzy_spatial_mappings WHERE entity_id IN %s",
                                  (tuple(set(m[0] for m in spatial_mappings)),))
                
                spatial_insert_sql = """
                INSERT INTO fuzzy_spatial_mappings (
                    entity_id, interpretation, latitude, longitude, location_type,
                    confidence, country, region, city, created_at
                ) VALUES %s;
                """
                
                execute_values(self.cursor, spatial_insert_sql, spatial_mappings, page_size=100)
                print(f"✅ 插入 {len(spatial_mappings)} 条空间映射记录")
            
            self.conn.commit()
            print("🎉 所有数据插入成功")
            
        except Exception as e:
            self.conn.rollback()
            print(f"❌ 数据插入失败: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description="模糊时空数据存储")
    parser.add_argument("--parquet-path", default="ragtest/output/entities.parquet", 
                       help="entities.parquet文件路径")
    parser.add_argument("--host", default="localhost", help="PostgreSQL主机")
    parser.add_argument("--port", default="5432", help="PostgreSQL端口")
    parser.add_argument("--database", default="graphrag", help="数据库名")
    parser.add_argument("--user", default="postgres", help="用户名")
    parser.add_argument("--password", default="", help="密码")
    parser.add_argument("--drop-tables", action="store_true", help="删除现有表重新创建")
    
    args = parser.parse_args()
    
    # 数据库配置
    db_config = {
        'host': args.host,
        'port': args.port,
        'database': args.database,
        'user': args.user,
        'password': args.password
    }
    
    storage = FuzzySpatiotemporalStorage(db_config)
    
    try:
        # 连接数据库
        storage.connect()
        
        # 创建表
        storage.create_fuzzy_spatiotemporal_tables(args.drop_tables)
        
        # 加载和处理数据
        main_data, temporal_mappings, spatial_mappings = storage.load_and_process_entities(args.parquet_path)
        
        # 插入数据
        storage.insert_data(main_data, temporal_mappings, spatial_mappings)
        
        print(f"\n🎉 模糊时空数据存储完成！")
        print(f"💡 现在可以使用SQL查询模糊和确定性的时空数据")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
    finally:
        storage.disconnect()

if __name__ == "__main__":
    main()
